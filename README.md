# StyleMe - AI Fashion Preview

Una web app di fashion styling che utilizza l'intelligenza artificiale per creare preview di outfit su foto personali.

## Caratteristiche

- 🖼️ Caricamento foto personale (intera o mezzo busto)
- 👕 Caricamento indumenti per testa, busto e gambe
- 🤖 Generazione AI di preview di stile usando Gemini 2.5 Flash Image Preview
- 💾 Download dell'immagine generata
- 📱 Design responsive e moderno
- ✨ Interfaccia fashion-oriented

## Tecnologie Utilizzate

- React 18
- Vite
- Framer Motion (animazioni)
- Lucide React (icone)
- OpenRouter API con Gemini 2.5 Flash Image Preview

## Setup Locale

1. **Clona il repository**
   ```bash
   git clone <repository-url>
   cd styleme-app
   ```

2. **Installa le dipendenze**
   ```bash
   npm install
   ```

3. **Configura le variabili d'ambiente**
   - Copia `.env.example` in `.env`
   - Inserisci la tua API key di OpenRouter:
   ```
   VITE_OPENROUTER_API_KEY=your_openrouter_api_key_here
   ```

4. **Avvia il server di sviluppo**
   ```bash
   npm run dev
   ```

## Build per Produzione

```bash
npm run build
```

I file di build saranno generati nella cartella `dist/`.

## Deploy su Netlify

### Metodo 1: Deploy da GitHub (Raccomandato)

1. **Push su GitHub**
   ```bash
   git add .
   git commit -m "Initial commit"
   git push origin main
   ```

2. **Connetti a Netlify**
   - Vai su [Netlify](https://netlify.com)
   - Clicca "New site from Git"
   - Connetti il tuo repository GitHub
   - Configura:
     - Build command: `npm run build`
     - Publish directory: `dist`

3. **Configura le variabili d'ambiente su Netlify**
   - Vai nelle impostazioni del sito
   - Sezione "Environment variables"
   - Aggiungi: `VITE_OPENROUTER_API_KEY` con la tua API key

### Metodo 2: Deploy Manuale

1. **Build locale**
   ```bash
   npm run build
   ```

2. **Deploy su Netlify**
   - Vai su [Netlify](https://netlify.com)
   - Trascina la cartella `dist` nell'area di deploy

## Configurazione API

### OpenRouter Setup

1. Registrati su [OpenRouter](https://openrouter.ai)
2. Ottieni la tua API key
3. Aggiungi la key nelle variabili d'ambiente

### Modello Utilizzato

- **Modello**: `google/gemini-2.5-flash-image-preview`
- **Endpoint**: `https://openrouter.ai/api/v1/chat/completions`
- **Documentazione**: [OpenRouter Gemini 2.5 Flash Image Preview](https://openrouter.ai/google/gemini-2.5-flash-image-preview/api)

## Struttura del Progetto

```
styleme-app/
├── public/
├── src/
│   ├── App.jsx          # Componente principale
│   ├── App.css          # Stili principali
│   ├── index.css        # Stili globali
│   └── main.jsx         # Entry point
├── .env.example         # Template variabili d'ambiente
├── netlify.toml         # Configurazione Netlify
└── package.json
```

## Funzionalità

1. **Upload Foto Utente**: Carica una foto personale (formato supportato: JPG, PNG)
2. **Upload Indumenti**: Carica fino a 3 indumenti per diverse parti del corpo
3. **Generazione AI**: Utilizza Gemini 2.5 Flash per creare la preview
4. **Download**: Scarica l'immagine generata
5. **Reset**: Riprova con nuove immagini

## Note Tecniche

- Le immagini vengono convertite in base64 per l'invio all'API
- Il design è completamente responsive
- Animazioni fluide con Framer Motion
- Gestione errori integrata

## Supporto

Per problemi o domande, consulta la documentazione di:
- [OpenRouter](https://openrouter.ai/docs)
- [Netlify](https://docs.netlify.com)
- [React](https://react.dev)
