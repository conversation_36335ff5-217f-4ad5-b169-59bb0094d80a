# Guida al Deployment - StyleMe

## 🚀 Deploy Rapido su Netlify

### Prerequisiti
- Account GitHub
- Account Netlify
- API Key di OpenRouter

### Passo 1: Preparazione Repository GitHub

1. **Inizializza Git** (se non già fatto):
   ```bash
   git init
   git add .
   git commit -m "Initial commit: StyleMe Fashion Preview App"
   ```

2. **Crea repository su GitHub**:
   - Vai su GitHub.com
   - Clicca "New repository"
   - Nome: `styleme-fashion-app`
   - Descrizione: "AI Fashion Preview App with Gemini 2.5 Flash"
   - Pubblico o Privato (a tua scelta)

3. **Push del codice**:
   ```bash
   git remote add origin https://github.com/TUO_USERNAME/styleme-fashion-app.git
   git branch -M main
   git push -u origin main
   ```

### Passo 2: Deploy su Netlify

1. **Accedi a Netlify**:
   - Vai su [netlify.com](https://netlify.com)
   - Accedi con GitHub

2. **Nuovo sito da Git**:
   - <PERSON>lic<PERSON> "New site from Git"
   - <PERSON><PERSON>li "GitHub"
   - Seleziona il repository `styleme-fashion-app`

3. **Configurazione Build**:
   - Build command: `npm run build`
   - Publish directory: `dist`
   - Clicca "Deploy site"

### Passo 3: Configurazione Variabili d'Ambiente

1. **Nelle impostazioni Netlify**:
   - Vai su "Site settings" > "Environment variables"
   - Clicca "Add variable"
   - Key: `VITE_OPENROUTER_API_KEY`
   - Value: La tua API key di OpenRouter

2. **Redeploy**:
   - Vai su "Deploys"
   - Clicca "Trigger deploy" > "Deploy site"

### Passo 4: Configurazione Dominio (Opzionale)

1. **Dominio personalizzato**:
   - Vai su "Domain settings"
   - Clicca "Add custom domain"
   - Inserisci il tuo dominio

## 🔧 Configurazione OpenRouter

### Ottenere l'API Key

1. **Registrazione**:
   - Vai su [openrouter.ai](https://openrouter.ai)
   - Crea un account
   - Verifica l'email

2. **Generare API Key**:
   - Vai su "API Keys"
   - Clicca "Create API Key"
   - Copia la chiave generata

3. **Configurare Crediti**:
   - Aggiungi crediti al tuo account
   - Il modello Gemini 2.5 Flash Image Preview ha costi variabili

## 📱 Test dell'App

### Funzionalità da Testare

1. **Upload Foto Utente**:
   - Carica una foto personale
   - Verifica che l'anteprima sia corretta

2. **Upload Indumenti**:
   - Carica almeno un indumento
   - Testa tutti e tre gli slot (testa, busto, gambe)

3. **Generazione AI**:
   - Clicca "Genera Preview"
   - Verifica che non ci siano errori API

4. **Download**:
   - Testa il download dell'immagine generata

5. **Reset**:
   - Testa il pulsante "Riprova"

## 🐛 Risoluzione Problemi

### Errori Comuni

1. **"API Key non configurata"**:
   - Verifica che la variabile d'ambiente sia impostata
   - Controlla che il nome sia esatto: `VITE_OPENROUTER_API_KEY`

2. **"Errore API: 401"**:
   - API Key non valida o scaduta
   - Verifica i crediti su OpenRouter

3. **"Errore API: 429"**:
   - Rate limit superato
   - Attendi qualche minuto prima di riprovare

4. **Build fallisce**:
   - Verifica che tutte le dipendenze siano installate
   - Controlla la versione di Node.js (minimo 18.x)

### Log e Debug

1. **Console del Browser**:
   - Apri Developer Tools (F12)
   - Controlla la console per errori JavaScript

2. **Network Tab**:
   - Verifica le chiamate API
   - Controlla i codici di risposta

3. **Netlify Logs**:
   - Vai su "Functions" > "Edge Functions"
   - Controlla i log di deploy

## 🔄 Aggiornamenti

### Deploy Automatico

Ogni push su `main` triggerà automaticamente un nuovo deploy su Netlify.

### Deploy Manuale

1. **Build locale**:
   ```bash
   npm run build
   ```

2. **Deploy manuale**:
   - Trascina la cartella `dist` su Netlify

## 📊 Monitoraggio

### Analytics Netlify

1. **Attiva Analytics**:
   - Vai su "Analytics"
   - Attiva il servizio

2. **Metriche da Monitorare**:
   - Visite uniche
   - Page views
   - Tempo di caricamento

### Costi OpenRouter

1. **Dashboard**:
   - Monitora l'uso dell'API
   - Imposta limiti di spesa

2. **Ottimizzazione**:
   - Riduci la risoluzione delle immagini se necessario
   - Implementa cache per richieste simili

## 🎯 Prossimi Passi

1. **Miglioramenti UI/UX**:
   - Aggiungere più animazioni
   - Migliorare il responsive design

2. **Funzionalità Avanzate**:
   - Salvataggio delle combinazioni preferite
   - Condivisione sui social media
   - Galleria di outfit generati

3. **Performance**:
   - Implementare lazy loading
   - Ottimizzare le immagini
   - Aggiungere service worker per PWA

## 📞 Supporto

Per problemi tecnici:
- Controlla la documentazione di [OpenRouter](https://openrouter.ai/docs)
- Consulta la guida di [Netlify](https://docs.netlify.com)
- Verifica i log di deploy e runtime
