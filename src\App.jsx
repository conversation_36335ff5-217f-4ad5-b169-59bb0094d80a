import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Upload, 
  Image, 
  Palette, 
  RefreshCw, 
  Download, 
  Home, 
  Sparkles,
  PaintBucket,
  FileText
} from 'lucide-react'
import { API_CONFIG, getApi<PERSON>ey, createColoringPrompt, prepareImagesForAPI } from './config/api'
import './App.css'

function App() {
  const [drawingImage, setDrawingImage] = useState(null)
  const [colorStyleImage, setColorStyleImage] = useState(null)
  const [drawingStyleImage, setDrawingStyleImage] = useState(null)
  const [coloringInstructions, setColoringInstructions] = useState('')
  const [generatedImage, setGeneratedImage] = useState(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState(null)

  const handleImageUpload = (event, setImageFunction) => {
    const file = event.target.files[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => setImageFunction(e.target.result)
      reader.readAsDataURL(file)
    }
  }

  const generateColoredDrawing = async () => {
    if (!drawingImage) {
      setError('Carica prima il disegno da colorare!')
      return
    }

    if (!coloringInstructions.trim()) {
      setError('Inserisci le istruzioni per la colorazione!')
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const apiKey = getApiKey()
      const images = prepareImagesForAPI(drawingImage, colorStyleImage, drawingStyleImage)
      const prompt = createColoringPrompt(
        coloringInstructions, 
        !!colorStyleImage, 
        !!drawingStyleImage
      )

      const response = await fetch(`${API_CONFIG.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          ...API_CONFIG.headers
        },
        body: JSON.stringify({
          model: API_CONFIG.model,
          messages: [
            {
              role: 'user',
              content: [
                {
                  type: 'text',
                  text: prompt
                },
                ...images.map(image => ({
                  type: 'image_url',
                  image_url: {
                    url: image
                  }
                }))
              ]
            }
          ],
          max_tokens: API_CONFIG.maxTokens,
          temperature: 0.7
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(`Errore API: ${response.status} - ${errorData.error?.message || 'Errore sconosciuto'}`)
      }

      const data = await response.json()
      console.log('API Response completa:', JSON.stringify(data, null, 2))
      
      if (data.choices && data.choices[0] && data.choices[0].message) {
        const message = data.choices[0].message
        console.log('Message completo:', JSON.stringify(message, null, 2))
        
        let imageFound = false
        
        // Controlla il campo images nel message
        if (message.images && Array.isArray(message.images) && message.images.length > 0) {
          for (const imageItem of message.images) {
            if (imageItem.type === 'image_url' && imageItem.image_url && imageItem.image_url.url) {
              console.log('Immagine trovata nel campo images!')
              setGeneratedImage(imageItem.image_url.url)
              imageFound = true
              break
            }
          }
        }
        
        // Cerca nel content del messaggio
        if (!imageFound && message.content) {
          const content = message.content
          console.log('Cercando immagine nel content, lunghezza:', content.length)
          
          const base64Patterns = [
            /data:image\/[^;]+;base64,[A-Za-z0-9+/=]+/g,
            /base64,[A-Za-z0-9+/=]{100,}/g,
            /[A-Za-z0-9+/=]{500,}/g
          ]
          
          for (const pattern of base64Patterns) {
            const matches = content.match(pattern)
            if (matches && matches.length > 0) {
              let base64Data = matches[0]
              console.log('Base64 trovato, lunghezza:', base64Data.length)
              
              if (!base64Data.startsWith('data:image/')) {
                if (base64Data.startsWith('base64,')) {
                  base64Data = 'data:image/png;' + base64Data
                } else {
                  base64Data = base64Data.replace(/^[^A-Za-z0-9+/=]*/, '')
                  base64Data = 'data:image/png;base64,' + base64Data
                }
              }
              
              console.log('Immagine base64 processata, lunghezza finale:', base64Data.length)
              setGeneratedImage(base64Data)
              imageFound = true
              break
            }
          }
          
          if (!imageFound && content.length > 100) {
            console.log('Tentativo di trattare tutto il content come base64...')
            try {
              const cleanBase64 = content.replace(/[^A-Za-z0-9+/=]/g, '')
              if (cleanBase64.length > 100) {
                const fullBase64 = 'data:image/png;base64,' + cleanBase64
                console.log('Base64 pulito creato, lunghezza:', fullBase64.length)
                setGeneratedImage(fullBase64)
                imageFound = true
              }
            } catch (e) {
              console.log('Errore nel processare come base64:', e)
            }
          }
          
          if (!imageFound) {
            const urlPattern = /https?:\/\/[^\s]+\.(jpg|jpeg|png|gif|webp)/gi
            const urlMatches = content.match(urlPattern)
            
            if (urlMatches && urlMatches.length > 0) {
              setGeneratedImage(urlMatches[0])
              imageFound = true
            }
          }
        }
        
        // Controlla altri possibili campi della risposta
        if (!imageFound) {
          console.log('Cercando immagine in altri campi...')
          
          if (message.image) {
            setGeneratedImage(message.image)
            imageFound = true
          } else if (message.image_url) {
            setGeneratedImage(message.image_url)
            imageFound = true
          } else if (data.image) {
            setGeneratedImage(data.image)
            imageFound = true
          }
        }
        
        if (!imageFound) {
          console.log('Nessuna immagine trovata. Struttura completa della risposta:', data)
          setError('L\'AI ha processato la richiesta ma l\'immagine colorata non è stata trovata nel formato atteso.')
        }
        
      } else {
        throw new Error('Risposta API non valida')
      }

    } catch (err) {
      console.error('Errore durante la generazione:', err)
      setError(err.message)
    } finally {
      setIsLoading(false)
    }
  }

  const downloadImage = () => {
    if (generatedImage) {
      try {
        const link = document.createElement('a')
        link.href = generatedImage
        link.download = `disegno-colorato-${Date.now()}.png`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      } catch (error) {
        console.error('Errore durante il download:', error)
        setError('Errore durante il download dell\'immagine')
      }
    }
  }

  const resetApp = () => {
    setDrawingImage(null)
    setColorStyleImage(null)
    setDrawingStyleImage(null)
    setColoringInstructions('')
    setGeneratedImage(null)
    setError(null)
  }

  return (
    <div className="app">
      <div className="background-pattern"></div>

      <header className="header">
        <motion.div
          className="logo"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <PaintBucket className="logo-icon" />
          <h1>ColorArt</h1>
          <p>AI Coloring Assistant</p>
        </motion.div>
      </header>

      <main className="main-content">
        {!generatedImage ? (
          <div className="upload-section">
            <div className="upload-grid">
              {/* Drawing Upload */}
              <motion.div
                className="upload-card"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
              >
                <div className="upload-header">
                  <Image className="upload-icon" />
                  <h3>Disegno da Colorare</h3>
                </div>

                {drawingImage ? (
                  <div className="photo-preview">
                    <img src={drawingImage} alt="Drawing" />
                    <button
                      className="change-photo-btn"
                      onClick={() => document.getElementById('drawing-image').click()}
                    >
                      Cambia Disegno
                    </button>
                  </div>
                ) : (
                  <label className="upload-area" htmlFor="drawing-image">
                    <Upload className="upload-placeholder-icon" />
                    <span>Carica il disegno</span>
                    <small>Disegno in bianco e nero o da colorare</small>
                  </label>
                )}

                <input
                  id="drawing-image"
                  type="file"
                  accept="image/*"
                  onChange={(e) => handleImageUpload(e, setDrawingImage)}
                  style={{ display: 'none' }}
                />
              </motion.div>

              {/* Color Style Upload */}
              <motion.div
                className="upload-card"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                <div className="upload-header">
                  <Palette className="upload-icon" />
                  <h3>Stile Colori (Opzionale)</h3>
                </div>

                {colorStyleImage ? (
                  <div className="photo-preview">
                    <img src={colorStyleImage} alt="Color Style" />
                    <button
                      className="change-photo-btn"
                      onClick={() => document.getElementById('color-style').click()}
                    >
                      Cambia Stile
                    </button>
                  </div>
                ) : (
                  <label className="upload-area" htmlFor="color-style">
                    <Upload className="upload-placeholder-icon" />
                    <span>Carica riferimento colori</span>
                    <small>Immagine con i colori da usare</small>
                  </label>
                )}

                <input
                  id="color-style"
                  type="file"
                  accept="image/*"
                  onChange={(e) => handleImageUpload(e, setColorStyleImage)}
                  style={{ display: 'none' }}
                />
              </motion.div>

              {/* Drawing Style Upload */}
              <motion.div
                className="upload-card"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <div className="upload-header">
                  <Sparkles className="upload-icon" />
                  <h3>Stile Artistico (Opzionale)</h3>
                </div>

                {drawingStyleImage ? (
                  <div className="photo-preview">
                    <img src={drawingStyleImage} alt="Drawing Style" />
                    <button
                      className="change-photo-btn"
                      onClick={() => document.getElementById('drawing-style').click()}
                    >
                      Cambia Stile
                    </button>
                  </div>
                ) : (
                  <label className="upload-area" htmlFor="drawing-style">
                    <Upload className="upload-placeholder-icon" />
                    <span>Carica stile artistico</span>
                    <small>Riferimento per lo stile del disegno</small>
                  </label>
                )}

                <input
                  id="drawing-style"
                  type="file"
                  accept="image/*"
                  onChange={(e) => handleImageUpload(e, setDrawingStyleImage)}
                  style={{ display: 'none' }}
                />
              </motion.div>

              {/* Instructions */}
              <motion.div
                className="upload-card instructions-card"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <div className="upload-header">
                  <FileText className="upload-icon" />
                  <h3>Istruzioni per la Colorazione</h3>
                </div>

                <textarea
                  className="instructions-textarea"
                  value={coloringInstructions}
                  onChange={(e) => setColoringInstructions(e.target.value)}
                  placeholder="Descrivi come vuoi colorare il disegno: colori specifici, stile, atmosfera, dettagli particolari..."
                  required
                />
              </motion.div>
            </div>

            {/* Generate Button */}
            <motion.button
              className="generate-btn"
              onClick={generateColoredDrawing}
              disabled={isLoading || !drawingImage || !coloringInstructions.trim()}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {isLoading ? (
                <>
                  <RefreshCw className="btn-icon spinning" />
                  Colorando...
                </>
              ) : (
                <>
                  <PaintBucket className="btn-icon" />
                  Colora Disegno
                </>
              )}
            </motion.button>

            {error && (
              <motion.div
                className="error-message"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
              >
                {error}
              </motion.div>
            )}
          </div>
        ) : (
          <motion.div
            className="result-section"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
          >
            <div className="result-header">
              <h2>Il Tuo Disegno Colorato</h2>
            </div>

            <div className="result-image">
              <img src={generatedImage} alt="Colored Drawing" />
            </div>

            <div className="result-actions">
              <motion.button
                className="download-btn"
                onClick={downloadImage}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Download className="btn-icon" />
                Scarica Immagine
              </motion.button>

              <motion.button
                className="home-btn"
                onClick={resetApp}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Home className="btn-icon" />
                Nuovo Disegno
              </motion.button>

              <motion.button
                className="retry-btn"
                onClick={generateColoredDrawing}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <RefreshCw className="btn-icon" />
                Ricolora
              </motion.button>
            </div>
          </motion.div>
        )}
      </main>
    </div>
  )
}

export default App

