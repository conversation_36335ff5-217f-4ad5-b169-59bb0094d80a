// API Configuration for OpenRouter
export const API_CONFIG = {
  baseURL: 'https://openrouter.ai/api/v1',
  model: 'google/gemini-2.5-flash-image-preview',
  maxTokens: 1000,
  headers: {
    'Content-Type': 'application/json',
    'HTTP-Referer': window.location.origin,
    'X-Title': 'ColorArt AI Assistant'
  }
}

// Get API key from environment variables
export const getApiKey = () => {
  const apiKey = import.meta.env.VITE_OPENROUTER_API_KEY
  if (!apiKey) {
    throw new Error('API Key non configurata. Aggiungi VITE_OPENROUTER_API_KEY nel file .env')
  }
  return apiKey
}

// Utility function to create coloring prompt
export const createColoringPrompt = (coloringInstructions, hasColorStyle, hasDrawingStyle) => {
  let prompt = "I need you to generate and return a new image. Take the drawing from the first provided image and color it following these specific instructions: "
  
  prompt += coloringInstructions + ". "
  
  if (hasColorStyle) {
    prompt += "Use the color palette and style from the second image as reference for the coloring approach. "
  }
  
  if (hasDrawingStyle) {
    const styleImageIndex = hasColorStyle ? "third" : "second"
    prompt += `Apply the artistic style and technique from the ${styleImageIndex} image to enhance the drawing while keeping the original structure intact. `
  }
  
  prompt += "IMPORTANT: Keep the original drawing structure, lines, and composition exactly the same. Only add colors and maintain the same perspective, proportions, and details. "
  prompt += "Do not modify the drawing itself, only color it. Generate and return the final colored image. Do not provide text descriptions, only return the generated image."
  
  return prompt
}

// Utility function to prepare images for API
export const prepareImagesForAPI = (drawingImage, colorStyleImage = null, drawingStyleImage = null) => {
  const images = [drawingImage]
  
  if (colorStyleImage) {
    images.push(colorStyleImage)
  }
  
  if (drawingStyleImage) {
    images.push(drawingStyleImage)
  }
  
  return images
}



