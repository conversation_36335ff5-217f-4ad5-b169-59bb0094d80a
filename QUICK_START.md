# 🚀 Quick Start - StyleMe Fashion Preview

## ⚡ Setup Veloce (5 minuti)

### 1. Configura l'API Key
```bash
# Copia il file di esempio
cp .env.example .env

# Modifica .env e inserisci la tua API key di OpenRouter
VITE_OPENROUTER_API_KEY=your_actual_api_key_here
```

### 2. Installa e Avvia
```bash
npm install
npm run dev
```

### 3. Testa l'App
- Apri http://localhost:5173
- Carica una tua foto
- Carica almeno un indumento
- Clicca "Genera Preview"

## 🌐 Deploy su Netlify (10 minuti)

### Metodo Rapido
1. **Push su GitHub**:
   ```bash
   git init
   git add .
   git commit -m "StyleMe Fashion App"
   git remote add origin https://github.com/TUO_USERNAME/styleme-app.git
   git push -u origin main
   ```

2. **Deploy su Netlify**:
   - Vai su [netlify.com](https://netlify.com)
   - "New site from Git" → Scegli il tuo repo
   - Build command: `npm run build`
   - Publish directory: `dist`

3. **Aggiungi API Key**:
   - Site settings → Environment variables
   - Aggiungi: `VITE_OPENROUTER_API_KEY` = la tua chiave

## 🔑 Ottenere l'API Key OpenRouter

1. Vai su [openrouter.ai](https://openrouter.ai)
2. Registrati/Accedi
3. Vai su "API Keys" → "Create API Key"
4. Copia la chiave generata
5. Aggiungi crediti al tuo account

## ✅ Checklist Pre-Deploy

- [ ] API Key configurata in `.env`
- [ ] App testata localmente
- [ ] Build completata senza errori (`npm run build`)
- [ ] Repository GitHub creato
- [ ] Variabili d'ambiente configurate su Netlify

## 🎯 Funzionalità Principali

- **Upload Foto**: Carica la tua foto (intera o mezzo busto)
- **Upload Indumenti**: Aggiungi vestiti per testa, busto, gambe
- **AI Preview**: Genera anteprima con Gemini 2.5 Flash
- **Download**: Scarica l'immagine risultante
- **Reset**: Riprova con nuove immagini

## 🐛 Problemi Comuni

### "API Key non configurata"
- Verifica che `.env` contenga `VITE_OPENROUTER_API_KEY=...`
- Su Netlify: controlla Environment Variables

### "Errore API: 401"
- API Key non valida
- Verifica crediti su OpenRouter

### Build fallisce
- Esegui `npm install` per installare dipendenze
- Verifica versione Node.js (minimo 18.x)

## 📱 Test dell'App

1. **Carica Foto Utente** ✓
2. **Carica Almeno 1 Indumento** ✓
3. **Genera Preview** ✓
4. **Scarica Immagine** ✓
5. **Testa Reset** ✓

## 🎨 Personalizzazioni

### Colori (src/App.css)
```css
:root {
  --primary-color: #d4af37;    /* Oro */
  --accent-color: #ff6b9d;     /* Rosa */
  --background-color: #fafafa; /* Bianco sporco */
}
```

### Prompt AI (src/config/api.js)
Modifica `createFashionPrompt()` per personalizzare le istruzioni per l'AI.

## 📊 Monitoraggio

- **Netlify Analytics**: Attiva nelle impostazioni del sito
- **OpenRouter Usage**: Monitora su openrouter.ai/usage
- **Console Browser**: F12 per debug errori

## 🔄 Aggiornamenti

Ogni push su `main` triggerà automaticamente un nuovo deploy su Netlify.

## 📞 Supporto

- **Documentazione OpenRouter**: [openrouter.ai/docs](https://openrouter.ai/docs)
- **Guida Netlify**: [docs.netlify.com](https://docs.netlify.com)
- **React Docs**: [react.dev](https://react.dev)

---

**🎉 La tua app StyleMe è pronta! Buon styling!**
