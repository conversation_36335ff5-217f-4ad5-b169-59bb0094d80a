/* Art and Coloring-inspired color palette */
:root {
  --primary-color: #6366f1;
  --secondary-color: #2c2c2c;
  --accent-color: #f59e0b;
  --background-color: #fafafa;
  --card-background: #ffffff;
  --text-primary: #2c2c2c;
  --text-secondary: #666666;
  --border-color: #e0e0e0;
  --shadow-light: 0 4px 20px rgba(0, 0, 0, 0.08);
  --shadow-medium: 0 8px 30px rgba(0, 0, 0, 0.12);
  --gradient-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  --gradient-accent: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background-color: var(--background-color);
  color: var(--text-primary);
  line-height: 1.6;
}

.app {
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

.background-pattern {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(245, 158, 11, 0.1) 0%, transparent 50%);
  z-index: -1;
}

.header {
  text-align: center;
  padding: 2rem 1rem;
  background: var(--gradient-primary);
  color: white;
  position: relative;
}

.header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 20px;
  background: linear-gradient(45deg, transparent 49%, white 50%, transparent 51%);
}

.logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.logo-icon {
  width: 3rem;
  height: 3rem;
  margin-bottom: 0.5rem;
}

.logo h1 {
  font-size: 3rem;
  font-weight: 700;
  letter-spacing: -0.02em;
  margin: 0;
}

.logo p {
  font-size: 1.1rem;
  opacity: 0.9;
  font-weight: 300;
}

.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 3rem 1rem;
}

.upload-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  align-items: center;
}

.upload-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  width: 100%;
  max-width: 1000px;
}

.upload-card {
  background: var(--card-background);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.upload-card:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-2px);
}

.upload-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.upload-icon {
  width: 1.5rem;
  height: 1.5rem;
  color: var(--primary-color);
}

.upload-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  border: 2px dashed var(--border-color);
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  padding: 2rem;
}

.upload-area:hover {
  border-color: var(--primary-color);
  background-color: rgba(99, 102, 241, 0.02);
}

.upload-placeholder-icon {
  width: 3rem;
  height: 3rem;
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

.upload-area span {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.upload-area small {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.photo-preview {
  position: relative;
  border-radius: 15px;
  overflow: hidden;
}

.photo-preview img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 15px;
}

.change-photo-btn {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.change-photo-btn:hover {
  background: rgba(0, 0, 0, 0.9);
}

.instructions-card {
  grid-column: 1 / -1;
  max-width: 600px;
  margin: 0 auto;
}

.instructions-textarea {
  width: 100%;
  min-height: 120px;
  padding: 1rem;
  border: 2px solid var(--border-color);
  border-radius: 10px;
  font-size: 1rem;
  font-family: inherit;
  resize: vertical;
  transition: border-color 0.3s ease;
}

.instructions-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
}

.instructions-textarea::placeholder {
  color: var(--text-secondary);
}

.generate-btn {
  background: var(--gradient-accent);
  color: white;
  border: none;
  padding: 1rem 2.5rem;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  box-shadow: var(--shadow-light);
  margin-top: 1rem;
}

.generate-btn:hover:not(:disabled) {
  box-shadow: var(--shadow-medium);
  transform: translateY(-2px);
}

.generate-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-icon {
  width: 1.25rem;
  height: 1.25rem;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.error-message {
  background: #fee;
  color: #c33;
  padding: 1rem 1.5rem;
  border-radius: 10px;
  border: 1px solid #fcc;
  font-weight: 500;
  text-align: center;
  max-width: 600px;
}

.result-section {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.result-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 2rem;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.result-image {
  background: var(--card-background);
  border-radius: 20px;
  padding: 1rem;
  box-shadow: var(--shadow-medium);
  margin-bottom: 2rem;
}

.result-image img {
  width: 100%;
  max-height: 600px;
  object-fit: contain;
  border-radius: 15px;
}

.result-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.download-btn,
.home-btn,
.retry-btn {
  background: var(--gradient-primary);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: var(--shadow-light);
}

.download-btn:hover,
.home-btn:hover,
.retry-btn:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .logo h1 {
    font-size: 2.5rem;
  }

  .main-content {
    padding: 2rem 1rem;
  }

  .upload-grid {
    grid-template-columns: 1fr;
    max-width: 400px;
  }

  .result-header h2 {
    font-size: 2rem;
  }

  .result-actions {
    flex-direction: column;
    align-items: center;
  }

  .download-btn,
  .home-btn,
  .retry-btn {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
}


